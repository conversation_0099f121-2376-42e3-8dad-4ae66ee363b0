.profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
}

.profile-card {
  width: 100%;
  max-width: 600px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 30px;
  overflow: hidden;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.profile-avatar-container {
  margin-right: 20px;
}

.profile-avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: linear-gradient(45deg, #6a11cb 0%, #2575fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
  font-weight: bold;
  box-shadow: 0 4px 20px rgba(106, 17, 203, 0.3);
}

.profile-info h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.profile-info h2 {
  margin: 5px 0;
  font-size: 16px;
  font-weight: 500;
  color: #6a11cb;
}

.location {
  margin: 0;
  font-size: 14px;
  color: #777;
  display: flex;
  align-items: center;
}

.profile-bio {
  margin-bottom: 24px;
  color: #555;
  line-height: 1.6;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #777;
  margin-top: 5px;
}

.profile-skills h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 18px;
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 24px;
}

.skill-tag {
  padding: 6px 12px;
  background: linear-gradient(45deg, #6a11cb 0%, #2575fc 100%);
  background-size: 200% auto;
  color: white;
  border-radius: 20px;
  font-size: 12px;
  transition: background-position 0.3s ease;
}

.skill-tag:hover {
  background-position: right center;
}

.edit-profile-button {
  width: 100%;
  padding: 12px;
  background: linear-gradient(45deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-profile-button:hover {
  background: linear-gradient(45deg, #5910b5 0%, #1b68e0 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(106, 17, 203, 0.3);
}

.floating-action-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  box-shadow: 0 5px 15px rgba(106, 17, 203, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.floating-action-button:hover {
  box-shadow: 0 8px 25px rgba(106, 17, 203, 0.4);
}

/* Add responsive styles */
@media (max-width: 480px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-avatar-container {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .profile-stats {
    padding: 10px 0;
  }
  
  .skill-tag {
    font-size: 11px;
    padding: 5px 10px;
  }
} 