import 'package:flutter/material.dart';

class GettingStartedHelpPage extends StatefulWidget {
  const GettingStartedHelpPage({Key? key}) : super(key: key);

  @override
  State<GettingStartedHelpPage> createState() => _GettingStartedHelpPageState();
}

class _GettingStartedHelpPageState extends State<GettingStartedHelpPage> {
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Getting Started',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
            color: Colors.white,
          ),
        ),
        backgroundColor: isDarkMode
            ? const Color(0xFF2C384A) // Dark blue-grey
            : const Color(0xFF4CAF50), // Material green
        elevation: 4.0,
        shadowColor: isDarkMode ? Colors.black : Colors.black26,
      ),
      body: Container(
        color: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F5F5),
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildHeaderSection(context),
            const SizedBox(height: 16),
            _buildSection(
              context,
              'Welcome to AlgoViz',
              'Start your journey with algorithm visualization:',
              [
                'Interactive visualization of various algorithms',
                'Step-by-step explanation of each process',
                'Customizable animation speeds',
                'Practice exercises and examples',
              ],
              Icons.home_outlined,
            ),
            _buildSection(
              context,
              'Creating Your Account',
              'Set up your personalized experience:',
              [
                'Sign up with email or social accounts',
                'Complete your profile information',
                'Choose your preferred settings',
                'Enable notifications for updates',
              ],
              Icons.person_add_outlined,
            ),
            _buildSection(
              context,
              'Basic Navigation',
              'Learn to navigate through the app:',
              [
                'Explore the main dashboard',
                'Access different algorithm categories',
                'Use the search functionality',
                'Customize your workspace',
              ],
              Icons.navigation_outlined,
            ),
            _buildSection(
              context,
              'First Steps',
              'Begin your learning journey:',
              [
                'Choose an algorithm to visualize',
                'Adjust visualization parameters',
                'Follow the step-by-step guide',
                'Practice with example problems',
              ],
              Icons.school_outlined,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? const Color(0xFF2C384A).withValues(alpha: 0.2)
            : const Color(0xFF4CAF50).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.green.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to AlgoViz!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.green[300] : Colors.green[900],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Get started with algorithm visualization. This guide will help you navigate through the basic features and functionalities of AlgoViz.',
            style: TextStyle(
              fontSize: 14,
              height: 1.5,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String subtitle, List<String> details, IconData icon) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: isDarkMode ? Colors.grey[800] : Colors.white,
      child: ExpansionTile(
        leading: Icon(
          icon,
          color: isDarkMode ? Colors.green[300] : Colors.green,
          size: 28,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 13,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: details.map((detail) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 18,
                      color: Theme.of(context).brightness == Brightness.dark ? Colors.green[300] : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        detail,
                        style: TextStyle(
                          height: 1.5,
                          fontSize: 14,
                          color: Theme.of(context).brightness == Brightness.dark ? Colors.grey[300] : Colors.grey[800],
                        ),
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ),
        ],
      ),
    );
  }
} 