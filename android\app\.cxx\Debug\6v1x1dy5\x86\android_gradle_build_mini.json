{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\flutter_application_2 final+updated today debo\\android\\app\\.cxx\\Debug\\6v1x1dy5\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\flutter_application_2 final+updated today debo\\android\\app\\.cxx\\Debug\\6v1x1dy5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}