{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\flutter_application_2 final+updated today debo - Copy - Copy\\android\\app\\.cxx\\Debug\\6i6p4b6o\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\flutter_application_2 final+updated today debo - Copy - Copy\\android\\app\\.cxx\\Debug\\6i6p4b6o\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}