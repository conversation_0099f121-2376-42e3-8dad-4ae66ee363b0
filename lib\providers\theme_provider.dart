import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class ThemeProvider with ChangeNotifier {
  AppTheme _currentTheme = AppTheme.light;

  AppTheme get currentTheme => _currentTheme;
  bool get isDarkMode => _currentTheme == AppTheme.dark;

  ThemeProvider() {
    // Initialize with light theme
    _currentTheme = AppTheme.light;
  }

  void setTheme(AppTheme theme) {
    if (_currentTheme != theme) {
      _currentTheme = theme;
      notifyListeners();
    }
  }

  void toggleTheme() {
    _currentTheme = _currentTheme == AppTheme.light ? AppTheme.dark : AppTheme.light;
    notifyListeners();
  }

  ThemeMode get themeMode => _currentTheme == AppTheme.light ? ThemeMode.light : ThemeMode.dark;

  ThemeData get lightTheme => AppThemeData.getThemeData(AppTheme.light);
  ThemeData get darkTheme => AppThemeData.getThemeData(AppTheme.dark);
}